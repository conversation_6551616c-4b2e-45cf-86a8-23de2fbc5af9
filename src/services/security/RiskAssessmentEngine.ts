/**
 * 风险评估引擎
 * 
 * 功能说明：
 * 1. 基于多维度数据进行风险评分
 * 2. 实时风险评估和动态调整
 * 3. 机器学习模型集成
 * 4. 威胁情报集成
 * 5. 自适应认证决策
 */

import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { Request } from 'express';
import { AnalyticsEngine } from '../analytics/AnalyticsEngine';

/**
 * 风险因子枚举
 */
export enum RiskFactor {
  LOCATION = 'location',
  DEVICE = 'device',
  TIME = 'time',
  BEHAVIOR = 'behavior',
  FREQUENCY = 'frequency',
  IP_REPUTATION = 'ip_reputation',
  USER_AGENT = 'user_agent',
  VELOCITY = 'velocity',
  GEOLOCATION = 'geolocation',
  THREAT_INTELLIGENCE = 'threat_intelligence'
}

/**
 * 风险级别枚举
 */
export enum RiskLevel {
  VERY_LOW = 'very_low',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'very_high',
  CRITICAL = 'critical'
}

/**
 * 认证强度枚举
 */
export enum AuthenticationStrength {
  BASIC = 'basic',           // 用户名密码
  MFA_SMS = 'mfa_sms',      // 短信验证
  MFA_TOTP = 'mfa_totp',    // TOTP验证
  MFA_PUSH = 'mfa_push',    // 推送验证
  BIOMETRIC = 'biometric',   // 生物识别
  HARDWARE_TOKEN = 'hardware_token' // 硬件令牌
}

/**
 * 风险评估结果接口
 */
export interface RiskAssessmentResult {
  userId: string;
  sessionId: string;
  overallScore: number; // 0-100，100为最高风险
  riskLevel: RiskLevel;
  factors: Array<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }>;
  recommendations: {
    requiredAuthStrength: AuthenticationStrength;
    additionalVerification: string[];
    allowAccess: boolean;
    blockReason?: string;
  };
  timestamp: Date;
  expiresAt: Date;
}

/**
 * 设备指纹接口
 */
export interface DeviceFingerprint {
  id: string;
  userId: string;
  fingerprint: string;
  components: {
    userAgent: string;
    screen: string;
    timezone: string;
    language: string;
    platform: string;
    plugins: string[];
    fonts: string[];
    canvas: string;
    webgl: string;
  };
  trustLevel: number; // 0-1
  firstSeen: Date;
  lastSeen: Date;
  usageCount: number;
}

/**
 * 风险评估引擎类
 */
export class RiskAssessmentEngine {
  private prisma: PrismaClient;
  private redis: Redis;
  private analyticsEngine: AnalyticsEngine;
  
  // 风险因子权重配置
  private readonly RISK_WEIGHTS = {
    [RiskFactor.LOCATION]: 0.2,
    [RiskFactor.DEVICE]: 0.15,
    [RiskFactor.TIME]: 0.1,
    [RiskFactor.BEHAVIOR]: 0.15,
    [RiskFactor.FREQUENCY]: 0.1,
    [RiskFactor.IP_REPUTATION]: 0.15,
    [RiskFactor.USER_AGENT]: 0.05,
    [RiskFactor.VELOCITY]: 0.1
  };

  // 风险阈值配置
  private readonly RISK_THRESHOLDS = {
    [RiskLevel.VERY_LOW]: 0,
    [RiskLevel.LOW]: 20,
    [RiskLevel.MEDIUM]: 40,
    [RiskLevel.HIGH]: 60,
    [RiskLevel.VERY_HIGH]: 80,
    [RiskLevel.CRITICAL]: 90
  };

  constructor(
    prisma: PrismaClient,
    redis: Redis,
    analyticsEngine: AnalyticsEngine
  ) {
    this.prisma = prisma;
    this.redis = redis;
    this.analyticsEngine = analyticsEngine;
  }

  /**
   * 执行风险评估
   */
  async assessRisk(
    userId: string,
    req: Request,
    context: Record<string, any> = {}
  ): Promise<RiskAssessmentResult> {
    try {
      const sessionId = req.session?.id || 'anonymous';
      
      // 并行计算各个风险因子
      const [
        locationRisk,
        deviceRisk,
        timeRisk,
        behaviorRisk,
        frequencyRisk,
        ipReputationRisk,
        userAgentRisk,
        velocityRisk
      ] = await Promise.all([
        this.assessLocationRisk(userId, req),
        this.assessDeviceRisk(userId, req),
        this.assessTimeRisk(userId, req),
        this.assessBehaviorRisk(userId, req),
        this.assessFrequencyRisk(userId, req),
        this.assessIPReputationRisk(req),
        this.assessUserAgentRisk(userId, req),
        this.assessVelocityRisk(userId, req)
      ]);

      const factors = [
        locationRisk,
        deviceRisk,
        timeRisk,
        behaviorRisk,
        frequencyRisk,
        ipReputationRisk,
        userAgentRisk,
        velocityRisk
      ];

      // 计算总体风险分数
      const overallScore = this.calculateOverallScore(factors);
      const riskLevel = this.determineRiskLevel(overallScore);
      const recommendations = this.generateRecommendations(riskLevel, factors);

      const result: RiskAssessmentResult = {
        userId,
        sessionId,
        overallScore,
        riskLevel,
        factors,
        recommendations,
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 5 * 60 * 1000) // 5分钟有效期
      };

      // 缓存结果
      await this.cacheRiskAssessment(result);

      // 记录风险评估事件
      await this.logRiskAssessment(result, req);

      return result;
    } catch (error) {
      console.error('风险评估失败:', error);
      throw error;
    }
  }

  /**
   * 评估位置风险
   */
  private async assessLocationRisk(userId: string, req: Request): Promise<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }> {
    const currentCountry = req.headers['cf-ipcountry'] || 'Unknown';
    const currentIP = req.ip || '127.0.0.1';

    // 获取用户历史位置数据
    const historicalLocations = await this.prisma.analyticsEvent.findMany({
      where: {
        userId,
        country: { not: null },
        timestamp: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30天内
        }
      },
      select: { country: true, ip: true },
      distinct: ['country', 'ip']
    });

    const knownCountries = [...new Set(historicalLocations.map(l => l.country))];
    const knownIPs = [...new Set(historicalLocations.map(l => l.ip))];

    let score = 0;
    let description = '位置风险正常';
    const evidence: Record<string, any> = {
      currentCountry,
      currentIP,
      knownCountries,
      knownIPs
    };

    // 检查国家/地区
    if (!knownCountries.includes(currentCountry)) {
      score += 40;
      description = '检测到来自新地理位置的访问';
      evidence.newCountry = true;
    }

    // 检查IP地址
    if (!knownIPs.includes(currentIP)) {
      score += 20;
      evidence.newIP = true;
    }

    // 检查地理位置跳跃（如果有上次位置记录）
    const lastLocation = await this.getLastKnownLocation(userId);
    if (lastLocation && lastLocation.country !== currentCountry) {
      const timeDiff = Date.now() - lastLocation.timestamp.getTime();
      if (timeDiff < 2 * 60 * 60 * 1000) { // 2小时内跨国
        score += 60;
        description = '检测到异常的地理位置跳跃';
        evidence.impossibleTravel = true;
      }
    }

    return {
      factor: RiskFactor.LOCATION,
      score: Math.min(100, score),
      weight: this.RISK_WEIGHTS[RiskFactor.LOCATION],
      description,
      evidence
    };
  }

  /**
   * 评估设备风险
   */
  private async assessDeviceRisk(userId: string, req: Request): Promise<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }> {
    const userAgent = req.get('User-Agent') || '';
    const deviceFingerprint = await this.generateDeviceFingerprint(req);

    // 获取用户已知设备
    const knownDevices = await this.prisma.analyticsEvent.findMany({
      where: {
        userId,
        userAgent: { not: null },
        timestamp: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        }
      },
      select: { userAgent: true, deviceType: true },
      distinct: ['userAgent']
    });

    const knownUserAgents = knownDevices.map(d => d.userAgent);
    
    let score = 0;
    let description = '设备风险正常';
    const evidence: Record<string, any> = {
      currentUserAgent: userAgent,
      deviceFingerprint: deviceFingerprint.id,
      knownDevices: knownUserAgents.length
    };

    // 检查User-Agent
    if (!knownUserAgents.includes(userAgent)) {
      score += 30;
      description = '检测到新设备访问';
      evidence.newDevice = true;
    }

    // 检查设备指纹
    const existingFingerprint = await this.findDeviceFingerprint(userId, deviceFingerprint.fingerprint);
    if (!existingFingerprint) {
      score += 40;
      evidence.newFingerprint = true;
    } else if (existingFingerprint.trustLevel < 0.5) {
      score += 20;
      evidence.lowTrustDevice = true;
    }

    return {
      factor: RiskFactor.DEVICE,
      score: Math.min(100, score),
      weight: this.RISK_WEIGHTS[RiskFactor.DEVICE],
      description,
      evidence
    };
  }

  /**
   * 评估时间风险
   */
  private async assessTimeRisk(userId: string, req: Request): Promise<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }> {
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay();

    // 获取用户历史活动时间模式
    const historicalActivity = await this.prisma.analyticsEvent.findMany({
      where: {
        userId,
        timestamp: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        }
      },
      select: { timestamp: true }
    });

    const activityHours = historicalActivity.map(a => a.timestamp.getHours());
    const activityDays = historicalActivity.map(a => a.timestamp.getDay());

    let score = 0;
    let description = '时间风险正常';
    const evidence: Record<string, any> = {
      currentHour,
      currentDay,
      historicalHours: [...new Set(activityHours)],
      historicalDays: [...new Set(activityDays)]
    };

    // 检查是否在常用时间段
    const hourFrequency = activityHours.filter(h => h === currentHour).length;
    const totalActivity = activityHours.length;
    
    if (totalActivity > 0 && hourFrequency / totalActivity < 0.05) { // 少于5%的活动在此时间
      score += 25;
      description = '在非常用时间段访问';
      evidence.unusualHour = true;
    }

    // 检查深夜访问（凌晨2-6点）
    if (currentHour >= 2 && currentHour <= 6) {
      score += 15;
      evidence.lateNightAccess = true;
    }

    return {
      factor: RiskFactor.TIME,
      score: Math.min(100, score),
      weight: this.RISK_WEIGHTS[RiskFactor.TIME],
      description,
      evidence
    };
  }

  /**
   * 评估行为风险
   */
  private async assessBehaviorRisk(userId: string, req: Request): Promise<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }> {
    // 检测异常行为
    const anomalies = await this.analyticsEngine.detectAnomalies(userId);
    
    let score = 0;
    let description = '行为风险正常';
    const evidence: Record<string, any> = {
      anomaliesDetected: anomalies.length,
      anomalies: anomalies.map(a => ({
        type: a.anomalyType,
        severity: a.severity,
        score: a.score
      }))
    };

    // 根据异常严重程度计算分数
    for (const anomaly of anomalies) {
      switch (anomaly.severity) {
        case 'low':
          score += 10;
          break;
        case 'medium':
          score += 25;
          break;
        case 'high':
          score += 50;
          break;
        case 'critical':
          score += 80;
          break;
      }
    }

    if (score > 0) {
      description = `检测到 ${anomalies.length} 个行为异常`;
    }

    return {
      factor: RiskFactor.BEHAVIOR,
      score: Math.min(100, score),
      weight: this.RISK_WEIGHTS[RiskFactor.BEHAVIOR],
      description,
      evidence
    };
  }

  /**
   * 评估频率风险
   */
  private async assessFrequencyRisk(userId: string, req: Request): Promise<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }> {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // 获取最近的活动频率
    const [hourlyActivity, dailyActivity] = await Promise.all([
      this.prisma.analyticsEvent.count({
        where: {
          userId,
          timestamp: { gte: oneHourAgo }
        }
      }),
      this.prisma.analyticsEvent.count({
        where: {
          userId,
          timestamp: { gte: oneDayAgo }
        }
      })
    ]);

    let score = 0;
    let description = '频率风险正常';
    const evidence: Record<string, any> = {
      hourlyActivity,
      dailyActivity
    };

    // 检查异常高频活动
    if (hourlyActivity > 100) { // 1小时内超过100次活动
      score += 60;
      description = '检测到异常高频活动';
      evidence.highFrequency = true;
    } else if (hourlyActivity > 50) {
      score += 30;
      evidence.moderateFrequency = true;
    }

    return {
      factor: RiskFactor.FREQUENCY,
      score: Math.min(100, score),
      weight: this.RISK_WEIGHTS[RiskFactor.FREQUENCY],
      description,
      evidence
    };
  }

  /**
   * 评估IP信誉风险
   */
  private async assessIPReputationRisk(req: Request): Promise<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }> {
    const ip = req.ip || '127.0.0.1';
    
    // 检查IP黑名单
    const isBlacklisted = await this.checkIPBlacklist(ip);
    
    // 检查IP类型（代理、VPN等）
    const ipInfo = await this.getIPInfo(ip);
    
    let score = 0;
    let description = 'IP信誉正常';
    const evidence: Record<string, any> = {
      ip,
      isBlacklisted,
      ipInfo
    };

    if (isBlacklisted) {
      score += 90;
      description = 'IP地址在黑名单中';
      evidence.blacklisted = true;
    }

    if (ipInfo.isProxy || ipInfo.isVPN) {
      score += 40;
      description = '检测到代理或VPN访问';
      evidence.proxy = true;
    }

    if (ipInfo.isTor) {
      score += 70;
      description = '检测到Tor网络访问';
      evidence.tor = true;
    }

    return {
      factor: RiskFactor.IP_REPUTATION,
      score: Math.min(100, score),
      weight: this.RISK_WEIGHTS[RiskFactor.IP_REPUTATION],
      description,
      evidence
    };
  }

  /**
   * 评估User-Agent风险
   */
  private async assessUserAgentRisk(userId: string, req: Request): Promise<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }> {
    const userAgent = req.get('User-Agent') || '';
    
    let score = 0;
    let description = 'User-Agent正常';
    const evidence: Record<string, any> = {
      userAgent
    };

    // 检查空或异常的User-Agent
    if (!userAgent || userAgent.length < 10) {
      score += 50;
      description = '缺失或异常的User-Agent';
      evidence.suspicious = true;
    }

    // 检查已知的恶意User-Agent模式
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /scanner/i,
      /curl/i,
      /wget/i
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(userAgent)) {
        score += 30;
        description = '检测到可疑的User-Agent';
        evidence.suspiciousPattern = pattern.source;
        break;
      }
    }

    return {
      factor: RiskFactor.USER_AGENT,
      score: Math.min(100, score),
      weight: this.RISK_WEIGHTS[RiskFactor.USER_AGENT],
      description,
      evidence
    };
  }

  /**
   * 评估速度风险（登录尝试频率）
   */
  private async assessVelocityRisk(userId: string, req: Request): Promise<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }> {
    const ip = req.ip || '127.0.0.1';
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    // 检查最近5分钟的登录尝试
    const recentAttempts = await this.prisma.analyticsEvent.count({
      where: {
        ip,
        type: { in: ['user_login', 'auth_failure'] },
        timestamp: { gte: fiveMinutesAgo }
      }
    });

    let score = 0;
    let description = '登录速度正常';
    const evidence: Record<string, any> = {
      recentAttempts,
      timeWindow: '5分钟'
    };

    if (recentAttempts > 10) {
      score += 80;
      description = '检测到暴力破解尝试';
      evidence.bruteForce = true;
    } else if (recentAttempts > 5) {
      score += 40;
      description = '登录尝试频率较高';
      evidence.highVelocity = true;
    }

    return {
      factor: RiskFactor.VELOCITY,
      score: Math.min(100, score),
      weight: this.RISK_WEIGHTS[RiskFactor.VELOCITY],
      description,
      evidence
    };
  }

  /**
   * 计算总体风险分数
   */
  private calculateOverallScore(factors: Array<{
    factor: RiskFactor;
    score: number;
    weight: number;
    description: string;
    evidence: Record<string, any>;
  }>): number {
    let weightedSum = 0;
    let totalWeight = 0;

    for (const factor of factors) {
      weightedSum += factor.score * factor.weight;
      totalWeight += factor.weight;
    }

    return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 0;
  }

  /**
   * 确定风险级别
   */
  private determineRiskLevel(score: number): RiskLevel {
    if (score >= this.RISK_THRESHOLDS[RiskLevel.CRITICAL]) return RiskLevel.CRITICAL;
    if (score >= this.RISK_THRESHOLDS[RiskLevel.VERY_HIGH]) return RiskLevel.VERY_HIGH;
    if (score >= this.RISK_THRESHOLDS[RiskLevel.HIGH]) return RiskLevel.HIGH;
    if (score >= this.RISK_THRESHOLDS[RiskLevel.MEDIUM]) return RiskLevel.MEDIUM;
    if (score >= this.RISK_THRESHOLDS[RiskLevel.LOW]) return RiskLevel.LOW;
    return RiskLevel.VERY_LOW;
  }

  /**
   * 生成认证建议
   */
  private generateRecommendations(
    riskLevel: RiskLevel,
    factors: Array<{ factor: RiskFactor; score: number; evidence: Record<string, any> }>
  ): {
    requiredAuthStrength: AuthenticationStrength;
    additionalVerification: string[];
    allowAccess: boolean;
    blockReason?: string;
  } {
    const additionalVerification: string[] = [];
    let requiredAuthStrength = AuthenticationStrength.BASIC;
    let allowAccess = true;
    let blockReason: string | undefined;

    switch (riskLevel) {
      case RiskLevel.VERY_LOW:
      case RiskLevel.LOW:
        requiredAuthStrength = AuthenticationStrength.BASIC;
        break;

      case RiskLevel.MEDIUM:
        requiredAuthStrength = AuthenticationStrength.MFA_SMS;
        additionalVerification.push('短信验证码');
        break;

      case RiskLevel.HIGH:
        requiredAuthStrength = AuthenticationStrength.MFA_TOTP;
        additionalVerification.push('TOTP验证', '邮箱验证');
        break;

      case RiskLevel.VERY_HIGH:
        requiredAuthStrength = AuthenticationStrength.MFA_PUSH;
        additionalVerification.push('推送验证', '安全问题', '管理员审批');
        break;

      case RiskLevel.CRITICAL:
        allowAccess = false;
        blockReason = '风险级别过高，访问被阻止';
        break;
    }

    // 根据具体风险因子添加额外验证
    for (const factor of factors) {
      if (factor.score > 50) {
        switch (factor.factor) {
          case RiskFactor.LOCATION:
            additionalVerification.push('地理位置验证');
            break;
          case RiskFactor.DEVICE:
            additionalVerification.push('设备验证');
            break;
          case RiskFactor.IP_REPUTATION:
            if (factor.evidence.blacklisted) {
              allowAccess = false;
              blockReason = 'IP地址在黑名单中';
            }
            break;
        }
      }
    }

    return {
      requiredAuthStrength,
      additionalVerification: [...new Set(additionalVerification)],
      allowAccess,
      blockReason
    };
  }

  // 辅助方法（简化实现）
  private async getLastKnownLocation(userId: string): Promise<{ country: string; timestamp: Date } | null> {
    const lastEvent = await this.prisma.analyticsEvent.findFirst({
      where: { userId, country: { not: null } },
      orderBy: { timestamp: 'desc' },
      select: { country: true, timestamp: true }
    });

    return lastEvent ? { country: lastEvent.country!, timestamp: lastEvent.timestamp } : null;
  }

  private async generateDeviceFingerprint(req: Request): Promise<DeviceFingerprint> {
    // 简化的设备指纹生成
    const userAgent = req.get('User-Agent') || '';
    const acceptLanguage = req.get('Accept-Language') || '';
    const acceptEncoding = req.get('Accept-Encoding') || '';
    
    const fingerprintData = `${userAgent}|${acceptLanguage}|${acceptEncoding}`;
    const fingerprint = Buffer.from(fingerprintData).toString('base64');

    return {
      id: `fp_${Date.now()}`,
      userId: '',
      fingerprint,
      components: {
        userAgent,
        screen: '',
        timezone: '',
        language: acceptLanguage,
        platform: '',
        plugins: [],
        fonts: [],
        canvas: '',
        webgl: ''
      },
      trustLevel: 0.5,
      firstSeen: new Date(),
      lastSeen: new Date(),
      usageCount: 1
    };
  }

  private async findDeviceFingerprint(userId: string, fingerprint: string): Promise<DeviceFingerprint | null> {
    // 实际实现中应该查询设备指纹数据库
    return null;
  }

  private async checkIPBlacklist(ip: string): Promise<boolean> {
    // 实际实现中应该查询IP黑名单数据库或第三方服务
    return false;
  }

  private async getIPInfo(ip: string): Promise<{
    isProxy: boolean;
    isVPN: boolean;
    isTor: boolean;
    country: string;
    isp: string;
  }> {
    // 实际实现中应该调用IP信息服务
    return {
      isProxy: false,
      isVPN: false,
      isTor: false,
      country: 'Unknown',
      isp: 'Unknown'
    };
  }

  private async cacheRiskAssessment(result: RiskAssessmentResult): Promise<void> {
    const key = `risk_assessment:${result.userId}:${result.sessionId}`;
    await this.redis.setex(key, 300, JSON.stringify(result)); // 5分钟缓存
  }

  private async logRiskAssessment(result: RiskAssessmentResult, req: Request): Promise<void> {
    // 记录风险评估日志
    console.log(`风险评估完成 - 用户: ${result.userId}, 风险级别: ${result.riskLevel}, 分数: ${result.overallScore}`);
  }
}

export default RiskAssessmentEngine;
